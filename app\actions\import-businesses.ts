"use server"

import { createClient } from "@supabase/supabase-js"

// Ensure environment variables are available and not empty
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// --- ADDED FOR DEBUGGING ---
console.log("DEBUG: app/actions/import-businesses.ts - NEXT_PUBLIC_SUPABASE_URL:", supabaseUrl ? "SET" : "NOT SET")
console.log(
  "DEBUG: app/actions/import-businesses.ts - SUPABASE_SERVICE_ROLE_KEY (masked):",
  supabaseServiceRoleKey ? "SET (length: " + supabaseServiceRoleKey.length + ")" : "NOT SET",
)
// --- END DEBUGGING ---

if (!supabaseUrl || supabaseUrl.trim() === "") {
  throw new Error("NEXT_PUBLIC_SUPABASE_URL environment variable is missing or empty.")
}
if (!supabaseServiceRoleKey || supabaseServiceRoleKey.trim() === "") {
  throw new Error("SUPABASE_SERVICE_ROLE_KEY environment variable is missing or empty.")
}

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

interface GooglePlacesBusiness {
  title: string
  totalScore?: number
  reviewsCount?: number
  street?: string
  city?: string
  state?: string
  countryCode?: string
  website?: string
  phone?: string
  categoryName?: string
  url?: string
}

// Helper function for delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// Helper function for retries
async function retrySupabaseCall<T>(
  call: () => Promise<{ data: T | null; error: any }>,
  maxRetries = 10, // Further increased max retries
  initialDelay = 500, // Further increased initial delay
): Promise<{ data: T | null; error: any }> {
  let retries = 0
  while (retries < maxRetries) {
    try {
      const result = await call()
      if (!result.error || result.error.code === "PGRST116") {
        // Success or "no rows found" (expected for inserts)
        return result
      }

      // Log Supabase-specific errors that are not "no rows found"
      console.warn(
        `Supabase call failed (retry ${retries + 1}/${maxRetries}): Supabase Error - ${result.error.message}`,
      )
      retries++
      await delay(initialDelay * Math.pow(2, retries - 1)) // Exponential backoff
    } catch (e: any) {
      // Catch any error thrown during the call, including SyntaxError from non-JSON response
      console.warn(`Supabase call failed (retry ${retries + 1}/${maxRetries}): General Error - ${e.message}`)
      retries++
      await delay(initialDelay * Math.pow(2, retries - 1)) // Exponential backoff
    }
  }
  // If all retries fail, make one last attempt and return its result
  return call()
}

export async function importBusinessesFromJSON(jsonData: GooglePlacesBusiness[]) {
  const results = {
    processed: 0,
    errors: 0,
    incomplete: 0,
    errorMessages: [] as string[],
  }

  for (const business of jsonData) {
    try {
      // Check for minimum required info for public listing
      const hasMinimumRequiredFields = business.title && business.city && business.state

      let stateId: number | null = null
      let cityId: number | null = null
      const currentMissingFields: string[] = []

      if (business.state) {
        // Try to find state by name
        const { data: stateData, error: stateLookupError } = await retrySupabaseCall(() =>
          supabase.from("states").select("id, code").eq("name", business.state).single(),
        )

        if (stateLookupError) {
          if (stateLookupError.code === "PGRST116") {
            // No rows found, attempt to insert it
            const stateCode =
              business.state.length === 2 ? business.state.toUpperCase() : business.state.substring(0, 2).toUpperCase() // Simple heuristic for code
            const { data: newStatData, error: insertStateError } = await retrySupabaseCall(() =>
              supabase.from("states").insert({ name: business.state, code: stateCode }).select("id").single(),
            )
            if (insertStateError) {
              console.error(`Error inserting state ${business.state}:`, insertStateError)
              currentMissingFields.push(`state_unresolvable: ${insertStateError.message}`)
            } else {
              stateId = newStatData?.id || null
            }
          } else {
            // Other Supabase error during state lookup
            console.error(`Supabase state lookup error for ${business.state}:`, stateLookupError)
            currentMissingFields.push(`state_lookup_failed: ${stateLookupError.message || "Unknown Supabase error"}`)
          }
        } else {
          stateId = stateData?.id || null
        }
      } else {
        currentMissingFields.push("state_missing")
      }

      if (business.city && stateId) {
        // Try to find city by name and state_id
        const { data: cityData, error: cityLookupError } = await retrySupabaseCall(() =>
          supabase.from("cities").select("id").eq("name", business.city).eq("state_id", stateId).single(),
        )

        if (cityLookupError) {
          if (cityLookupError.code === "23505") {
            // Unique constraint violation, city already exists for this state
            // This can happen if another concurrent import or manual entry created it
            const { data: existingCityData } = await supabase
              .from("cities")
              .select("id")
              .eq("name", business.city)
              .eq("state_id", stateId)
              .single()
            cityId = existingCityData?.id || null
            if (!cityId) {
              console.error(`City ${business.city} exists but could not retrieve ID after unique constraint error.`)
              currentMissingFields.push(`city_lookup_failed_after_duplicate: ${cityLookupError.message}`)
            }
          } else if (cityLookupError.code === "PGRST116") {
            // No rows found, attempt to insert it
            const { data: newCityData, error: insertCityError } = await retrySupabaseCall(() =>
              supabase.from("cities").insert({ name: business.city, state_id: stateId }).select("id").single(),
            )
            if (insertCityError) {
              console.error(`Error inserting city ${business.city} for state ${business.state}:`, insertCityError)
              currentMissingFields.push(`city_unresolvable: ${insertCityError.message}`)
            } else {
              cityId = newCityData?.id || null
            }
          } else {
            // Other Supabase error during city lookup
            console.error(
              `Supabase city lookup error for ${business.city} in state ${business.state}:`,
              cityLookupError,
            )
            currentMissingFields.push(`city_lookup_failed: ${cityLookupError.message || "Unknown Supabase error"}`)
          }
        } else {
          cityId = cityData?.id || null
        }
      } else if (!business.city) {
        currentMissingFields.push("city_missing")
      } else if (!stateId) {
        currentMissingFields.push("city_state_unresolved") // City present but state not resolved
      }

      // If any critical fields are missing or unresolvable, mark as incomplete
      if (!hasMinimumRequiredFields || currentMissingFields.length > 0 || !stateId || !cityId) {
        const incompleteData = {
          original_data: JSON.stringify(business),
          missing_fields:
            currentMissingFields.length > 0
              ? currentMissingFields.join(", ")
              : "title, city, or state missing/unresolvable",
          business_name: business.title || "Unknown Business",
          status: "pending_review",
          created_at: new Date().toISOString(),
        }

        const { error: incompleteError } = await retrySupabaseCall(() =>
          supabase.from("incomplete_businesses").insert({
            ...incompleteData,
            missing_fields: incompleteData.missing_fields, // Ensure it's a string
          }),
        )

        if (incompleteError) {
          console.error(`Error inserting incomplete business ${business.title || "Unknown"}:`, incompleteError)
          results.errorMessages.push(
            `Error saving incomplete business ${business.title || "Unknown"}: ${incompleteError.message}`,
          )
          results.errors++
        } else {
          results.incomplete++
        }
        continue // Skip to next business
      }

      // If we reach here, all required fields are present and location is resolved
      const slug = business.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "")

      const businessData = {
        name: business.title,
        slug: slug,
        description: `${business.categoryName || "Business"} serving ${business.city}, ${business.state}. ${business.reviewsCount ? `Rated ${business.totalScore}/5 with ${business.reviewsCount} reviews.` : ""}`,
        address: business.street || "", // address is NOT NULL, so "" is fine
        city_id: cityId,
        state_id: stateId, // Add state_id to the business data
        zip_code: null, // Assuming zip_code is not in Google Places data
        phone: business.phone || null,
        email: null, // Assuming email is not in Google Places data
        website: business.website || null,
        latitude: null, // Assuming lat/long not in Google Places data
        longitude: null, // Assuming lat/long not in Google Places data
        is_claimed: false,
        claimed_by: null,
        verified: false,
        rating: business.totalScore || 0,
        review_count: business.reviewsCount || 0,
        google_maps_url: business.url || null,
        category_name: business.categoryName || "Pressure washing service",
        country_code: business.countryCode || "US",
      }

      console.log("Attempting to insert business:", business.title, "with stateId:", stateId, "cityId:", cityId)
      const { error } = await retrySupabaseCall(() => supabase.from("businesses").insert(businessData))

      if (error) {
        if (error.code === "23505") {
          console.log(`Business ${business.title} already exists (slug: ${slug}), skipping...`)
        } else {
          console.error(`Error inserting business ${business.title}:`, error)
          results.errorMessages.push(`Error inserting business ${business.title}: ${error.message}`)
          results.errors++
        }
      } else {
        results.processed++
      }
    } catch (error: any) {
      results.errors++
      results.errorMessages.push(`Unhandled error processing ${business.title || "Unknown"}: ${error.message}`)
      console.error(`Unhandled error processing business ${business.title || "Unknown"}:`, error)

      // Don't stop processing on individual errors, but log them
      if (results.errors > 500) {
        // Increased threshold to avoid premature stopping
        results.errorMessages.push("Too many errors, stopping import...")
        break
      }
    }
  }

  return results
}

export async function getIncompleteBusinesses() {
  const { data, error } = await supabase
    .from("incomplete_businesses")
    .select("*")
    .order("created_at", { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch incomplete businesses: ${error.message}`)
  }

  return data || []
}

export async function updateIncompleteBusinessStatus(id: number, status: string, notes?: string) {
  const { error } = await supabase
    .from("incomplete_businesses")
    .update({
      status,
      notes,
      updated_at: new Date().toISOString(),
    })
    .eq("id", id)

  if (error) {
    throw new Error(`Failed to update business status: ${error.message}`)
  }

  return { success: true }
}
