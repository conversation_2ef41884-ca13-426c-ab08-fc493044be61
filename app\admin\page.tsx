"use client"

import type React from "react"

import { useState } from "react"
import { Upload, FileText, Users, Building, BarChart3 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Link from "next/link"

const mockStats = {
  totalBusinesses: 15247,
  claimedBusinesses: 8934,
  pendingClaims: 23,
  totalUsers: 2156,
}

const mockImportLogs = [
  {
    id: 1,
    filename: "florida_businesses_2024.json",
    status: "success",
    recordsProcessed: 1247,
    date: "2024-01-15 10:30:00",
    importedBy: "<EMAIL>",
  },
  {
    id: 2,
    filename: "texas_businesses_2024.json",
    status: "failed",
    error: "Invalid JSON format on line 45",
    date: "2024-01-14 14:22:00",
    importedBy: "<EMAIL>",
  },
  {
    id: 3,
    filename: "california_businesses_2024.json",
    status: "processing",
    recordsProcessed: 456,
    date: "2024-01-14 09:15:00",
    importedBy: "<EMAIL>",
  },
]

export default function AdminPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle")
  const [uploadMessage, setUploadMessage] = useState("")

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setUploadStatus("uploading")
    setUploadMessage("Processing file...")

    try {
      const fileContent = await selectedFile.text()
      const businessData = JSON.parse(fileContent)

      // Send to API for processing
      const response = await fetch("/api/businesses/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(businessData),
      })

      const result = await response.json()

      if (result.success) {
        setUploadStatus("success")
        setUploadMessage(
          `Successfully processed ${result.processed} businesses. ${result.errors > 0 ? `${result.errors} errors encountered.` : ""}`,
        )
      } else {
        setUploadStatus("error")
        setUploadMessage(`Import failed: ${result.error}`)
      }
    } catch (error) {
      setUploadStatus("error")
      setUploadMessage(`Failed to process file: ${error.message}`)
    }

    setSelectedFile(null)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return <Badge className="bg-green-500">Success</Badge>
      case "failed":
        return <Badge className="bg-red-500">Failed</Badge>
      case "processing":
        return <Badge className="bg-yellow-500">Processing</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, Admin</span>
              <Button variant="outline">Logout</Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Businesses</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.totalBusinesses.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+12% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Claimed Businesses</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.claimedBusinesses.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">58.6% claim rate</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Claims</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.pendingClaims}</div>
              <p className="text-xs text-muted-foreground">Requires review</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+8% from last month</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="upload" className="space-y-6">
          <TabsList>
            <TabsTrigger value="upload">JSON Upload</TabsTrigger>
            <TabsTrigger value="logs">Import Logs</TabsTrigger>
            <TabsTrigger value="incomplete">Incomplete Listings</TabsTrigger>
            <TabsTrigger value="businesses">Manage Businesses</TabsTrigger>
            <TabsTrigger value="claims">Pending Claims</TabsTrigger>
          </TabsList>

          <TabsContent value="upload">
            <Card>
              <CardHeader>
                <CardTitle>Upload Business Data</CardTitle>
                <p className="text-sm text-gray-600">
                  Upload JSON files to bulk import business listings. Ensure your JSON follows the required schema.
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                {uploadStatus !== "idle" && (
                  <Alert
                    className={
                      uploadStatus === "success"
                        ? "border-green-500"
                        : uploadStatus === "error"
                          ? "border-red-500"
                          : "border-yellow-500"
                    }
                  >
                    <AlertDescription>{uploadMessage}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="file-upload">Select JSON File</Label>
                    <Input id="file-upload" type="file" accept=".json" onChange={handleFileSelect} className="mt-1" />
                  </div>

                  {selectedFile && (
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-blue-600 mr-2" />
                        <span className="font-medium">{selectedFile.name}</span>
                        <span className="ml-2 text-sm text-gray-600">({(selectedFile.size / 1024).toFixed(1)} KB)</span>
                      </div>
                    </div>
                  )}

                  <Button
                    onClick={handleUpload}
                    disabled={!selectedFile || uploadStatus === "uploading"}
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {uploadStatus === "uploading" ? "Processing..." : "Upload and Process"}
                  </Button>
                </div>

                <div className="border-t pt-6">
                  <h4 className="font-medium mb-2">JSON Schema Example:</h4>
                  <Textarea
                    readOnly
                    value={`[
  {
    "title": "ABC Pressure Washing",
    "totalScore": 4.8,
    "reviewsCount": 127,
    "street": "123 Main St",
    "city": "Phoenix",
    "state": "Arizona",
    "countryCode": "US",
    "website": "http://abcpressure.com/",
    "phone": "(*************",
    "categoryName": "Pressure washing service",
    "url": "https://www.google.com/maps/search/?api=1&query=ABC%20Pressure%20Washing"
  }
]`}
                    className="h-40 font-mono text-sm"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs">
            <Card>
              <CardHeader>
                <CardTitle>Import History</CardTitle>
                <p className="text-sm text-gray-600">Track all JSON import operations and their status.</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockImportLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-gray-400" />
                          <div>
                            <div className="font-medium">{log.filename}</div>
                            <div className="text-sm text-gray-600">
                              Imported by {log.importedBy} on {log.date}
                            </div>
                            {log.recordsProcessed && (
                              <div className="text-sm text-gray-600">{log.recordsProcessed} records processed</div>
                            )}
                            {log.error && <div className="text-sm text-red-600">{log.error}</div>}
                          </div>
                        </div>
                      </div>
                      <div>{getStatusBadge(log.status)}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="incomplete">
            <Card>
              <CardHeader>
                <CardTitle>Incomplete Business Listings</CardTitle>
                <p className="text-sm text-gray-600">
                  Businesses that were imported but are missing required information.
                </p>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Manage Incomplete Listings</h3>
                  <p className="text-gray-600 mb-4">
                    Review businesses that need additional information to be published.
                  </p>
                  <Link href="/admin/incomplete">
                    <Button>View Incomplete Listings</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="businesses">
            <Card>
              <CardHeader>
                <CardTitle>Manage Businesses</CardTitle>
                <p className="text-sm text-gray-600">View and manage all business listings in the directory.</p>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-gray-500">
                  Business management interface would be implemented here. Features would include search, edit, delete,
                  and bulk operations.
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="claims">
            <Card>
              <CardHeader>
                <CardTitle>Pending Business Claims</CardTitle>
                <p className="text-sm text-gray-600">Review and approve business ownership claims.</p>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-gray-500">
                  Claims management interface would be implemented here. Features would include claim review, approval,
                  and rejection workflows.
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
