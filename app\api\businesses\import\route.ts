import { type NextRequest, NextResponse } from "next/server"
import { importBusinessesFromJSON } from "@/app/actions/import-businesses"

export async function POST(request: NextRequest) {
  try {
    const jsonData = await request.json()

    // Validate that it's an array
    if (!Array.isArray(jsonData)) {
      return NextResponse.json({ error: "Invalid JSON format. Expected an array of businesses." }, { status: 400 })
    }

    const results = await importBusinessesFromJSON(jsonData)

    return NextResponse.json({
      success: true,
      processed: results.processed,
      errors: results.errors,
      incomplete: results.incomplete,
      errorMessages: results.errorMessages,
      message: `Successfully processed ${results.processed} businesses. ${results.incomplete} businesses saved for review due to missing information. ${results.errors} errors encountered.`,
    })
  } catch (error) {
    console.error("Import error:", error)
    return NextResponse.json({ error: "Failed to process import" }, { status: 500 })
  }
}
