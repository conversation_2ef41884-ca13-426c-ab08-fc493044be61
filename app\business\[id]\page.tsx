"use client"

import { ArrowLeft, Star, MapPin, Phone, Globe, Mail, Clock, Shield, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"
import { getBusinessById } from "@/lib/data" // Import data function

// Set revalidate time for ISR (e.g., 1 hour)
export const revalidate = 3600

export default async function BusinessDetailPage({ params }: { params: { id: string } }) {
  const business = await getBusinessById(params.id)

  if (!business) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center text-center p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Business Not Found</h1>
        <p className="text-lg text-gray-600 mb-8">
          The business you are looking for does not exist or has been removed.
        </p>
        <Link href="/">
          <Button>Back to Directory</Button>
        </Link>
      </div>
    )
  }

  // Helper to format hours
  const formatHours = (hoursArray: any[]) => {
    const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
    const formatted: { [key: string]: string } = {}
    days.forEach((day, index) => {
      const dayHours = hoursArray.find((h) => h.day_of_week === index)
      if (dayHours) {
        formatted[day.toLowerCase()] = dayHours.is_closed
          ? "Closed"
          : `${dayHours.open_time.substring(0, 5)} - ${dayHours.close_time.substring(0, 5)}`
      } else {
        formatted[day.toLowerCase()] = "N/A" // Default if no hours found for a day
      }
    })
    return formatted
  }

  const businessHours = formatHours(business.business_hours || [])
  const primaryImage =
    business.business_images?.find((img) => img.is_primary)?.image_url ||
    business.business_images?.[0]?.image_url ||
    `/placeholder.svg?height=400&width=600&text=${encodeURIComponent(business.name)}`
  const galleryImages = business.business_images?.map((img) => img.image_url) || []

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Directory
            </Link>
            <div className="flex items-center space-x-4">
              {!business.is_claimed && (
                <Link href={`/claim/${business.id}`}>
                  <Button>Claim This Business</Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Business Header */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-3xl mb-2">{business.name}</CardTitle>
                    <div className="flex items-center mb-2">
                      <div className="flex items-center mr-4">
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1 text-lg font-medium">{business.rating}</span>
                        <span className="ml-1 text-gray-500">({business.review_count} reviews)</span>
                      </div>
                      {business.is_claimed && (
                        <Badge className="bg-green-500">
                          <Shield className="h-3 w-3 mr-1" />
                          Verified Business
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      {business.address}, {business.cities?.name}, {business.states?.code} {business.zip_code}
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Gallery */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Gallery</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4">
                  <img
                    src={primaryImage || "/placeholder.svg"}
                    alt="Business gallery"
                    className="w-full h-96 object-cover rounded-lg"
                  />
                  {galleryImages.length > 0 && (
                    <div className="grid grid-cols-4 gap-2">
                      {galleryImages.map((image, index) => (
                        <img
                          key={index}
                          src={image || `/placeholder.svg?height=200&width=300&text=Gallery+${index + 1}`}
                          alt={`Gallery ${index + 1}`}
                          className={`h-20 object-cover rounded cursor-pointer`}
                          // onClick={() => setSelectedImage(index)} // Re-implement client-side state for image selection if needed
                        />
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Tabs defaultValue="about" className="mb-8">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="about">About</TabsTrigger>
                <TabsTrigger value="services">Services</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              <TabsContent value="about">
                <Card>
                  <CardHeader>
                    <CardTitle>About {business.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 leading-relaxed">
                      {business.description || "No description provided."}
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="services">
                <Card>
                  <CardHeader>
                    <CardTitle>Services Offered</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {business.business_services && business.business_services.length > 0 ? (
                        business.business_services.map((bs, index) => (
                          <div key={index} className="flex items-center p-3 bg-blue-50 rounded-lg">
                            <Award className="h-5 w-5 text-blue-600 mr-3" />
                            <span className="font-medium">{bs.services?.name}</span>
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-600">No services listed.</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reviews">
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Reviews</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {business.reviews && business.reviews.length > 0 ? (
                        business.reviews.map((review) => (
                          <div key={review.created_at} className="border-b pb-4 last:border-b-0">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <span className="font-medium">{review.users?.first_name || "Anonymous"}</span>
                                <div className="flex items-center ml-2">
                                  {[...Array(5)].map((_, i) => (
                                    <Star
                                      key={i}
                                      className={`h-4 w-4 ${
                                        i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                      }`}
                                    />
                                  ))}
                                </div>
                              </div>
                              <span className="text-sm text-gray-500">
                                {new Date(review.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            <p className="text-gray-600">{review.comment}</p>
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-600">No reviews yet.</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Contact Information */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {business.phone && (
                  <div className="flex items-center">
                    <Phone className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="font-medium">{business.phone}</div>
                      <div className="text-sm text-gray-500">Phone</div>
                    </div>
                  </div>
                )}
                {business.email && (
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="font-medium">{business.email}</div>
                      <div className="text-sm text-gray-500">Email</div>
                    </div>
                  </div>
                )}
                {business.website && (
                  <div className="flex items-center">
                    <Globe className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="font-medium">
                        <a
                          href={business.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {business.website}
                        </a>
                      </div>
                      <div className="text-sm text-gray-500">Website</div>
                    </div>
                  </div>
                )}
                {business.google_maps_url && (
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="font-medium">
                        <a
                          href={business.google_maps_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          View on Google Maps
                        </a>
                      </div>
                      <div className="text-sm text-gray-500">Map</div>
                    </div>
                  </div>
                )}
                <div className="pt-4">
                  <Button className="w-full mb-2">Get Quote</Button>
                  {business.website && (
                    <a href={business.website} target="_blank" rel="noopener noreferrer">
                      <Button variant="outline" className="w-full bg-transparent">
                        Visit Website
                      </Button>
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Business Hours */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Business Hours
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(businessHours).map(([day, hours]) => (
                    <div key={day} className="flex justify-between">
                      <span className="capitalize font-medium">{day}</span>
                      <span className="text-gray-600">{hours}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
