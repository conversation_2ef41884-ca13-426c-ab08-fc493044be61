"use client"

import { useState } from "react"
import { ArrowLeft, Search, MapPin, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

// Mock data for demonstration (similar to homepage businesses)
const mockBusinesses = [
  {
    id: "1",
    name: "Elite Pressure Washing",
    city: "Miami",
    state: "FL",
    description: "Professional residential and commercial pressure washing services with 15+ years of experience.",
    phone: "(*************",
    rating: 4.8,
    reviewCount: 127,
    services: ["Residential", "Commercial", "Roof Cleaning"],
    claimed: true, // This one is already claimed
    image: "/placeholder.svg?height=200&width=300&text=Elite+PW",
  },
  {
    id: "2",
    name: "Crystal Clean Power Wash",
    city: "Orlando",
    state: "FL",
    description: "Eco-friendly pressure washing solutions for homes and businesses throughout Central Florida.",
    phone: "(*************",
    rating: 4.6,
    reviewCount: 89,
    services: ["Residential", "Commercial", "Pool Deck Cleaning"],
    claimed: false,
    image: "/placeholder.svg?height=200&width=300&text=Crystal+Clean",
  },
  {
    id: "3",
    name: "ProWash Solutions",
    city: "Tampa",
    state: "FL",
    description: "Complete exterior cleaning services including pressure washing, soft washing, and gutter cleaning.",
    phone: "(*************",
    rating: 4.9,
    reviewCount: 203,
    services: ["Residential", "Commercial", "Soft Washing"],
    claimed: false,
    image: "/placeholder.svg?height=200&width=300&text=ProWash+Solutions",
  },
  {
    id: "4",
    name: "Sunshine Power Wash",
    city: "Jacksonville",
    state: "FL",
    description: "Reliable and affordable power washing for homes and businesses in Jacksonville.",
    phone: "(*************",
    rating: 4.5,
    reviewCount: 65,
    services: ["Residential", "Driveway Cleaning"],
    claimed: false,
    image: "/placeholder.svg?height=200&width=300&text=Sunshine+PW",
  },
]

export default function ClaimBusinessSearchPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState<typeof mockBusinesses>([])
  const [searched, setSearched] = useState(false)

  const handleSearch = () => {
    setSearched(true)
    // In a real app, this would make an API call to search for businesses
    const filtered = mockBusinesses.filter(
      (business) =>
        business.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.state.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    setSearchResults(filtered)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Directory
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Claim Your Business</h1>
            <div></div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Find Your Business Listing</CardTitle>
            <p className="text-sm text-gray-600">
              Enter your business name, city, or state to find your existing listing and claim it.
            </p>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Search business name, city, or state..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-12 text-lg"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleSearch()
                      }
                    }}
                  />
                </div>
              </div>
              <Button onClick={handleSearch} size="lg" className="h-12 px-8">
                <Search className="mr-2 h-5 w-5" />
                Search
              </Button>
            </div>
          </CardContent>
        </Card>

        {searched && searchResults.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Search Results</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {searchResults.map((business) => (
                <Card key={business.id} className="hover:shadow-xl transition-shadow">
                  <div className="relative">
                    <img
                      src={business.image || "/placeholder.svg"}
                      alt={business.name}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                    {business.claimed && <Badge className="absolute top-2 right-2 bg-green-500">Verified</Badge>}
                  </div>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-xl">{business.name}</CardTitle>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1 text-sm font-medium">{business.rating}</span>
                        <span className="ml-1 text-sm text-gray-500">({business.reviewCount})</span>
                      </div>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      {business.city}, {business.state}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {business.services.slice(0, 3).map((service) => (
                        <Badge key={service} variant="secondary">
                          {service}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex gap-2">
                      <Link href={`/business/${business.id}`} className="flex-1">
                        <Button variant="outline" className="w-full bg-transparent">
                          View Details
                        </Button>
                      </Link>
                      {!business.claimed ? (
                        <Link href={`/claim/${business.id}`}>
                          <Button variant="default">Claim This Business</Button>
                        </Link>
                      ) : (
                        <Button variant="secondary" disabled>
                          Already Claimed
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {searched && searchResults.length === 0 && (
          <Card className="mb-8">
            <CardContent className="p-6 text-center">
              <h3 className="text-xl font-semibold mb-4">No Businesses Found</h3>
              <p className="text-gray-600 mb-6">
                We couldn't find a business matching your search. Would you like to add your business to our directory?
              </p>
              <Link href="/list-business">
                <Button>List Your Business</Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {!searched && (
          <Card>
            <CardContent className="p-6 text-center">
              <h3 className="text-xl font-semibold mb-4">Can't find your business?</h3>
              <p className="text-gray-600 mb-6">
                If your business isn't listed, you can easily add it to our directory.
              </p>
              <Link href="/list-business">
                <Button>List Your Business</Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
