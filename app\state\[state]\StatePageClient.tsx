"use client"

import { <PERSON>, MapPin, Star, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { getStateByCode, getCitiesByStateId, getBusinessesByState } from "@/lib/data" // Import data functions
import { useEffect, useState } from "react"

export default function StatePageClient({ stateCode }: { stateCode: string }) {
  const [stateData, setStateData] = useState(null)
  const [cities, setCities] = useState([])
  const [businesses, setBusinesses] = useState([])

  useEffect(() => {
    const fetchData = async () => {
      const stateData = await getStateByCode(stateCode)
      setStateData(stateData)

      if (stateData) {
        const cities = await getCitiesByStateId(stateData.id)
        setCities(cities)

        const businesses = await getBusinessesByState(stateData.id)
        setBusinesses(businesses)
      }
    }

    fetchData()
  }, [stateCode])

  if (!stateData) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center text-center p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">State Not Found</h1>
        <p className="text-lg text-gray-600 mb-8">
          The state you are looking for does not exist in our directory or has an invalid code.
        </p>
        <Link href="/">
          <Button>Back to Home</Button>
        </Link>
      </div>
    )
  }

  // You would implement actual search/filter logic here,
  // potentially using client-side state and re-fetching or filtering `businesses` array.
  // For now, the search/filter UI elements are present but will only filter the initially loaded data.

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Directory
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Pressure Washing in {stateData.name}</h1>
            <div></div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* State Overview */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-3xl">Pressure Washing Services in {stateData.name}</CardTitle>
              <p className="text-lg text-gray-600">
                Find {businesses.length.toLocaleString()} professional pressure washing businesses across{" "}
                {stateData.name}
              </p>
            </CardHeader>
          </Card>
        </div>

        {/* Cities Grid */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by City</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {cities.map((city) => (
              <Link
                key={city.id}
                href={`/city/${stateCode.toLowerCase()}/${city.name.toLowerCase().replace(" ", "-")}`}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-4 text-center">
                    <div className="text-lg font-semibold text-gray-900">{city.name}</div>
                    {/* City business count would need a separate query or pre-calculated data */}
                    <div className="text-sm text-gray-600">N/A businesses</div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      placeholder="Search businesses..."
                      // value={searchTerm} // These would be client-side state
                      // onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select
                // value={selectedCity} // These would be client-side state
                // onValueChange={setSelectedCity}
                >
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Select City" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Cities">All Cities</SelectItem>
                    {cities.map((city) => (
                      <SelectItem key={city.id} value={city.name}>
                        {city.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                // value={sortBy} // These would be client-side state
                // onValueChange={setSortBy}
                >
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="rating">Rating</SelectItem>
                    <SelectItem value="city">City</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Business Listings */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {businesses.map((business) => (
            <Card key={business.id} className="hover:shadow-xl transition-shadow">
              <div className="relative">
                <img
                  src={`/placeholder.svg?height=200&width=300&text=${encodeURIComponent(business.name)}`} // Dynamic placeholder
                  alt={business.name}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                {business.is_claimed && <Badge className="absolute top-2 right-2 bg-green-500">Verified</Badge>}
              </div>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-xl">{business.name}</CardTitle>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="ml-1 text-sm font-medium">{business.rating}</span>
                    <span className="ml-1 text-sm text-gray-500">({business.review_count})</span>
                  </div>
                </div>
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-1" />
                  {business.cities?.name}, {business.states?.code}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                {/* Services would need to be fetched and displayed here */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {/* {business.services.slice(0, 3).map((service) => (
                    <Badge key={service} variant="secondary">
                      {service}
                    </Badge>
                  ))} */}
                </div>

                <div className="flex gap-2">
                  <Link href={`/business/${business.id}`} className="flex-1">
                    <Button variant="outline" className="w-full bg-transparent">
                      View Details
                    </Button>
                  </Link>
                  {!business.is_claimed && (
                    <Link href={`/claim/${business.id}`}>
                      <Button variant="default">Claim</Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Businesses
          </Button>
        </div>
      </div>
    </div>
  )
}
