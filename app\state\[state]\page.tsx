import StatePageClient from "./StatePageClient"
import { getStates } from "@/lib/data" // Import data functions

// Set revalidate time for ISR (e.g., 1 hour)
export const revalidate = 3600

// Generate static params for all states at build time
export async function generateStaticParams() {
  const states = await getStates()
  return states.map((state) => ({
    state: state.code.toLowerCase(),
  }))
}

export default async function StatePage({ params }: { params: { state: string } }) {
  const stateCode = params.state.toUpperCase()

  return <StatePageClient stateCode={stateCode} />
}
