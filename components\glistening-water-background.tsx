"use client"

import { useRef, useEffect, useCallback } from "react"

interface Ripple {
  x: number
  y: number
  radius: number
  opacity: number
  speed: number
}

export function GlisteningWaterBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameId = useRef<number | null>(null)
  const ripples = useRef<Ripple[]>([])
  const maxRipples = 30

  const createRipple = useCallback((width: number, height: number): Ripple => {
    return {
      x: Math.random() * width,
      y: Math.random() * height,
      radius: Math.random() * 10 + 10, // Slightly larger initial radius
      opacity: Math.random() * 0.4 + 0.3, // Slightly more noticeable opacity
      speed: Math.random() * 0.7 + 0.3, // Slightly faster speed
    }
  }, [])

  const draw = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Clear canvas with a semi-transparent overlay to create a fading trail effect
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Update and draw ripples
    ripples.current.forEach((ripple, index) => {
      // Move ripple
      ripple.y += ripple.speed
      ripple.radius += ripple.speed * 0.15 // Expand radius a bit faster
      ripple.opacity -= 0.008 // Fade out a bit faster

      // If ripple is off-screen or faded, reset it
      if (ripple.opacity <= 0 || ripple.y - ripple.radius > canvas.height) {
        ripples.current[index] = createRipple(canvas.width, canvas.height)
      }

      // Draw ripple
      ctx.beginPath()
      ctx.arc(ripple.x, ripple.y, ripple.radius, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(255, 255, 255, ${ripple.opacity})` // White glistening effect
      ctx.fill()
    })

    animationFrameId.current = requestAnimationFrame(draw)
  }, [createRipple])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const setCanvasDimensions = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
    }

    // Initialize ripples
    setCanvasDimensions()
    ripples.current = [] // Clear existing ripples on re-mount/resize
    for (let i = 0; i < maxRipples; i++) {
      ripples.current.push(createRipple(canvas.width, canvas.height))
    }

    // Start animation
    animationFrameId.current = requestAnimationFrame(draw)
    console.log("GlisteningWaterBackground mounted and animation started.") // Debugging log

    // Handle resize
    window.addEventListener("resize", setCanvasDimensions)

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
      window.removeEventListener("resize", setCanvasDimensions)
      console.log("GlisteningWaterBackground unmounted and animation stopped.") // Debugging log
    }
  }, [draw, createRipple])

  return (
    <canvas ref={canvasRef} className="absolute inset-0 w-full h-full z-0 pointer-events-none" aria-hidden="true" />
  )
}
