"use client"

import { useRef, useEffect, useCallback } from "react"

interface Particle {
  x: number
  y: number
  size: number
  speedY: number
  opacity: number
  color: string
}

export function GlowingFlowingWaterBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameId = useRef<number | null>(null)
  const particles = useRef<Particle[]>([])
  const maxParticles = 100 // Number of concurrent particles

  const createParticle = useCallback((width: number, height: number): Particle => {
    const baseColor = { r: 0, g: 191, b: 255 } // Bright blue
    const variation = Math.random() * 50 - 25 // Slight color variation
    const r = Math.max(0, Math.min(255, baseColor.r + variation))
    const g = Math.max(0, Math.min(255, baseColor.g + variation))
    const b = Math.max(0, Math.min(255, baseColor.b + variation))

    return {
      x: Math.random() * width,
      y: Math.random() * height * 0.2 - height * 0.2, // Start slightly above the top
      size: Math.random() * 3 + 1, // Smaller particles
      speedY: Math.random() * 1.5 + 0.5, // Flowing downwards speed
      opacity: Math.random() * 0.8 + 0.2, // Varying opacity for depth
      color: `rgb(${r}, ${g}, ${b})`,
    }
  }, [])

  const draw = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Clear canvas with a slight fade effect to create trails
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set up glow effect
    ctx.shadowBlur = 15 // Increased blur for stronger glow
    ctx.shadowColor = "rgba(0, 191, 255, 0.8)" // Blue glow color

    // Update and draw particles
    particles.current.forEach((particle, index) => {
      // Move particle
      particle.y += particle.speedY
      particle.opacity -= 0.005 // Fade out slowly

      // If particle is off-screen or faded, reset it
      if (particle.opacity <= 0 || particle.y > canvas.height + particle.size) {
        particles.current[index] = createParticle(canvas.width, canvas.height)
      }

      // Draw particle
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(0, 191, 255, ${particle.opacity})` // Use a consistent blue for fill
      ctx.fill()
    })

    // Reset shadow properties after drawing particles to avoid affecting other elements
    ctx.shadowBlur = 0
    ctx.shadowColor = "transparent"

    animationFrameId.current = requestAnimationFrame(draw)
  }, [createParticle])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const setCanvasDimensions = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
    }

    // Initialize particles
    setCanvasDimensions()
    particles.current = [] // Clear existing particles on re-mount/resize
    for (let i = 0; i < maxParticles; i++) {
      particles.current.push(createParticle(canvas.width, canvas.height))
    }

    // Start animation
    animationFrameId.current = requestAnimationFrame(draw)
    console.log("GlowingFlowingWaterBackground mounted and animation started.")

    // Handle resize
    window.addEventListener("resize", setCanvasDimensions)

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
      window.removeEventListener("resize", setCanvasDimensions)
      console.log("GlowingFlowingWaterBackground unmounted and animation stopped.")
    }
  }, [draw, createParticle])

  return (
    <canvas ref={canvasRef} className="absolute inset-0 w-full h-full z-0 pointer-events-none" aria-hidden="true" />
  )
}
