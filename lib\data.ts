import { createClient } from "@supabase/supabase-js"

// Ensure environment variables are available and not empty
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// --- ADDED FOR DEBUGGING ---
console.log("DEBUG: lib/data.ts - NEXT_PUBLIC_SUPABASE_URL:", supabaseUrl ? "SET" : "NOT SET")
console.log(
  "DEBUG: lib/data.ts - SUPABASE_SERVICE_ROLE_KEY (masked):",
  supabaseServiceRoleKey ? "SET (length: " + supabaseServiceRoleKey.length + ")" : "NOT SET",
)
// --- END DEBUGGING ---

if (!supabaseUrl || supabaseUrl.trim() === "") {
  throw new Error("NEXT_PUBLIC_SUPABASE_URL environment variable is missing or empty.")
}
if (!supabaseServiceRoleKey || supabaseServiceRoleKey.trim() === "") {
  throw new Error("SUPABASE_SERVICE_ROLE_KEY environment variable is missing or empty.")
}

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

// Helper function for retries (re-using from import-businesses.ts)
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

async function retrySupabaseCall<T>(
  call: () => Promise<{ data: T | null; error: any }>,
  maxRetries = 5,
  initialDelay = 200,
): Promise<{ data: T | null; error: any }> {
  let retries = 0
  while (retries < maxRetries) {
    try {
      const result = await call()
      if (!result.error || result.error.code === "PGRST116") {
        return result
      }
      console.warn(
        `Supabase call failed (retry ${retries + 1}/${maxRetries}): Supabase Error - ${result.error.message}`,
      )
      retries++
      await delay(initialDelay * Math.pow(2, retries - 1))
    } catch (e: any) {
      console.warn(`Supabase call failed (retry ${retries + 1}/${maxRetries}): General Error - ${e.message}`)
      retries++
      await delay(initialDelay * Math.pow(2, retries - 1))
    }
  }
  return call()
}

export async function getStates() {
  const { data, error } = await retrySupabaseCall(() =>
    supabase.from("states").select("id, name, code").order("name", { ascending: true }),
  )
  if (error) {
    console.error("Error fetching states:", error)
    return []
  }
  return data || []
}

export async function getStateByCode(code: string) {
  const { data, error } = await retrySupabaseCall(() =>
    supabase.from("states").select("id, name, code").eq("code", code.toUpperCase()).single(),
  )
  if (error && error.code !== "PGRST116") {
    // PGRST116 means no rows found, which is fine
    console.error(`Error fetching state with code ${code}:`, error)
  }
  return data
}

export async function getCitiesByStateId(stateId: number) {
  const { data, error } = await retrySupabaseCall(() =>
    supabase.from("cities").select("id, name").eq("state_id", stateId).order("name", { ascending: true }),
  )
  if (error) {
    console.error(`Error fetching cities for state ID ${stateId}:`, error)
    return []
  }
  return data || []
}

export async function getBusinessesByState(stateId: number, cityId?: number | null, searchTerm?: string) {
  let query = supabase
    .from("businesses")
    .select(
      "id, name, slug, description, phone, rating, review_count, is_claimed, google_maps_url, category_name, cities(name), states(code)",
    )
    .eq("state_id", stateId)
    .order("name", { ascending: true })

  if (cityId) {
    query = query.eq("city_id", cityId)
  }

  if (searchTerm) {
    query = query.ilike("name", `%${searchTerm}%`) // Case-insensitive search
  }

  const { data, error } = await retrySupabaseCall(() => query)
  if (error) {
    console.error(`Error fetching businesses for state ID ${stateId}:`, error)
    return []
  }
  return data || []
}

export async function getBusinessById(id: string) {
  const { data, error } = await retrySupabaseCall(() =>
    supabase
      .from("businesses")
      .select(
        `
        *,
        cities(name),
        states(name, code),
        business_services(services(name)),
        business_hours(day_of_week, open_time, close_time, is_closed),
        business_images(image_url, alt_text, is_primary),
        reviews(rating, comment, created_at, users(first_name, last_name))
      `,
      )
      .eq("id", id)
      .single(),
  )
  if (error && error.code !== "PGRST116") {
    console.error(`Error fetching business with ID ${id}:`, error)
  }
  return data
}

// You might want to add a function to get popular states with business counts
export async function getPopularStatesWithCounts(limit = 6) {
  // This query is a bit more complex as it requires aggregation.
  // For simplicity, we'll fetch all states and then count businesses per state
  // in a more optimized way if needed, or rely on a materialized view in Supabase.
  // For now, let's just get states and assume a mock count or fetch a rough count.
  const { data: statesData, error: statesError } = await retrySupabaseCall(() =>
    supabase.from("states").select("id, name, code").order("name", { ascending: true }).limit(limit),
  )

  if (statesError) {
    console.error("Error fetching popular states:", statesError)
    return []
  }

  // For a real count, you'd need a more efficient query or a materialized view.
  // This is a placeholder to simulate counts.
  const statesWithCounts =
    statesData?.map((state) => ({
      ...state,
      count: Math.floor(Math.random() * 1000) + 500, // Placeholder random count
    })) || []

  return statesWithCounts.sort((a, b) => b.count - a.count) // Sort by count
}
