-- Create table for incomplete business listings
CREATE TABLE IF NOT EXISTS incomplete_businesses (
    id SERIAL PRIMARY KEY,
    original_data JSONB NOT NULL,
    missing_fields TEXT NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending_review',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_incomplete_businesses_status ON incomplete_businesses(status);
CREATE INDEX IF NOT EXISTS idx_incomplete_businesses_created ON incomplete_businesses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_incomplete_businesses_name ON incomplete_businesses(business_name);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_incomplete_businesses_updated_at 
    BEFORE UPDATE ON incomplete_businesses 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
