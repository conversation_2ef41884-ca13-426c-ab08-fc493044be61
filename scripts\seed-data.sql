-- Insert initial states
INSERT INTO states (code, name) VALUES
('AL', 'Alabama'),
('AK', 'Alaska'),
('AZ', 'Arizona'),
('AR', 'Arkansas'),
('CA', 'California'),
('CO', 'Colorado'),
('CT', 'Connecticut'),
('DE', 'Delaware'),
('FL', 'Florida'),
('GA', 'Georgia'),
('HI', 'Hawaii'),
('ID', 'Idaho'),
('IL', 'Illinois'),
('IN', 'Indiana'),
('IA', 'Iowa'),
('KS', 'Kansas'),
('KY', 'Kentucky'),
('LA', 'Louisiana'),
('ME', 'Maine'),
('MD', 'Maryland'),
('MA', 'Massachusetts'),
('MI', 'Michigan'),
('MN', 'Minnesota'),
('MS', 'Mississippi'),
('MO', 'Missouri'),
('MT', 'Montana'),
('NE', 'Nebraska'),
('NV', 'Nevada'),
('NH', 'New Hampshire'),
('NJ', 'New Jersey'),
('NM', 'New Mexico'),
('NY', 'New York'),
('NC', 'North Carolina'),
('ND', 'North Dakota'),
('OH', 'Ohio'),
('OK', 'Oklahoma'),
('OR', 'Oregon'),
('PA', 'Pennsylvania'),
('RI', 'Rhode Island'),
('SC', 'South Carolina'),
('SD', 'South Dakota'),
('TN', 'Tennessee'),
('TX', 'Texas'),
('UT', 'Utah'),
('VT', 'Vermont'),
('VA', 'Virginia'),
('WA', 'Washington'),
('WV', 'West Virginia'),
('WI', 'Wisconsin'),
('WY', 'Wyoming');

-- Insert major cities for Florida (example)
INSERT INTO cities (name, state_id) VALUES
('Miami', (SELECT id FROM states WHERE code = 'FL')),
('Orlando', (SELECT id FROM states WHERE code = 'FL')),
('Tampa', (SELECT id FROM states WHERE code = 'FL')),
('Jacksonville', (SELECT id FROM states WHERE code = 'FL')),
('Fort Lauderdale', (SELECT id FROM states WHERE code = 'FL')),
('Tallahassee', (SELECT id FROM states WHERE code = 'FL')),
('St. Petersburg', (SELECT id FROM states WHERE code = 'FL')),
('Hialeah', (SELECT id FROM states WHERE code = 'FL')),
('Port St. Lucie', (SELECT id FROM states WHERE code = 'FL')),
('Cape Coral', (SELECT id FROM states WHERE code = 'FL'));

-- Insert common pressure washing services
INSERT INTO services (name, description) VALUES
('Residential Pressure Washing', 'House washing and residential exterior cleaning'),
('Commercial Pressure Washing', 'Commercial building and property cleaning'),
('Driveway Cleaning', 'Concrete and asphalt driveway pressure washing'),
('Roof Cleaning', 'Soft washing and roof maintenance'),
('Pool Deck Cleaning', 'Pool area and deck cleaning services'),
('Soft Washing', 'Low-pressure cleaning for delicate surfaces'),
('Gutter Cleaning', 'Gutter cleaning and maintenance'),
('Sidewalk Cleaning', 'Walkway and sidewalk pressure washing'),
('Parking Lot Cleaning', 'Commercial parking area cleaning'),
('Graffiti Removal', 'Graffiti cleaning and removal services'),
('Fleet Washing', 'Vehicle and equipment cleaning'),
('Window Cleaning', 'Exterior window cleaning services');

-- Insert admin user (password should be hashed in real application)
INSERT INTO users (email, password_hash, first_name, last_name, role) VALUES
('<EMAIL>', '$2b$10$example_hash', 'Admin', 'User', 'admin');

-- Insert sample businesses
INSERT INTO businesses (name, slug, description, address, city_id, zip_code, phone, email, website, latitude, longitude, is_claimed, rating, review_count) VALUES
('Elite Pressure Washing', 'elite-pressure-washing-miami', 'Professional residential and commercial pressure washing services with 15+ years of experience in the Miami area.', '123 Business Ave', (SELECT id FROM cities WHERE name = 'Miami'), '33101', '(*************', '<EMAIL>', 'www.elitepressurewashing.com', 25.7617, -80.1918, true, 4.8, 127),
('Crystal Clean Power Wash', 'crystal-clean-power-wash-orlando', 'Eco-friendly pressure washing solutions for homes and businesses throughout Central Florida.', '456 Clean St', (SELECT id FROM cities WHERE name = 'Orlando'), '32801', '(*************', '<EMAIL>', 'www.crystalcleanpw.com', 28.5383, -81.3792, false, 4.6, 89),
('ProWash Solutions', 'prowash-solutions-tampa', 'Complete exterior cleaning services including pressure washing, soft washing, and gutter cleaning.', '789 Wash Way', (SELECT id FROM cities WHERE name = 'Tampa'), '33602', '(*************', '<EMAIL>', 'www.prowashsolutions.com', 27.9506, -82.4572, true, 4.9, 203);

-- Link businesses to services
INSERT INTO business_services (business_id, service_id) VALUES
-- Elite Pressure Washing services
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), (SELECT id FROM services WHERE name = 'Residential Pressure Washing')),
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), (SELECT id FROM services WHERE name = 'Commercial Pressure Washing')),
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), (SELECT id FROM services WHERE name = 'Roof Cleaning')),
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), (SELECT id FROM services WHERE name = 'Driveway Cleaning')),

-- Crystal Clean Power Wash services
((SELECT id FROM businesses WHERE slug = 'crystal-clean-power-wash-orlando'), (SELECT id FROM services WHERE name = 'Residential Pressure Washing')),
((SELECT id FROM businesses WHERE slug = 'crystal-clean-power-wash-orlando'), (SELECT id FROM services WHERE name = 'Commercial Pressure Washing')),
((SELECT id FROM businesses WHERE slug = 'crystal-clean-power-wash-orlando'), (SELECT id FROM services WHERE name = 'Pool Deck Cleaning')),

-- ProWash Solutions services
((SELECT id FROM businesses WHERE slug = 'prowash-solutions-tampa'), (SELECT id FROM services WHERE name = 'Residential Pressure Washing')),
((SELECT id FROM businesses WHERE slug = 'prowash-solutions-tampa'), (SELECT id FROM services WHERE name = 'Commercial Pressure Washing')),
((SELECT id FROM businesses WHERE slug = 'prowash-solutions-tampa'), (SELECT id FROM services WHERE name = 'Soft Washing')),
((SELECT id FROM businesses WHERE slug = 'prowash-solutions-tampa'), (SELECT id FROM services WHERE name = 'Gutter Cleaning'));

-- Insert sample business hours (Monday = 1, Sunday = 0)
INSERT INTO business_hours (business_id, day_of_week, open_time, close_time) VALUES
-- Elite Pressure Washing hours
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 1, '08:00', '18:00'), -- Monday
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 2, '08:00', '18:00'), -- Tuesday
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 3, '08:00', '18:00'), -- Wednesday
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 4, '08:00', '18:00'), -- Thursday
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 5, '08:00', '18:00'), -- Friday
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 6, '09:00', '16:00'), -- Saturday
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 0, NULL, NULL, true); -- Sunday (closed)

-- Insert sample reviews
INSERT INTO reviews (business_id, rating, title, comment, is_verified) VALUES
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 5, 'Excellent Service!', 'They cleaned our driveway and it looks brand new. Very professional and reasonably priced.', true),
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 5, 'Highly Recommend', 'Elite Pressure Washing did an amazing job on our commercial building. Will definitely use again!', true),
((SELECT id FROM businesses WHERE slug = 'elite-pressure-washing-miami'), 4, 'Great Work', 'Professional team that arrived on time and did thorough work on our roof cleaning.', true);
