-- Add state_id column to businesses table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='businesses' AND column_name='state_id') THEN
        ALTER TABLE businesses ADD COLUMN state_id INTEGER NULL; -- Explicitly nullable
    END IF;
END
$$;

-- Add foreign key constraint for state_id if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'businesses_state_id_fkey') THEN
        ALTER TABLE businesses ADD CONSTRAINT businesses_state_id_fkey FOREIGN KEY (state_id) REFERENCES states(id);
    END IF;
END
$$;

-- Create index for state_id if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_businesses_state ON businesses(state_id);

-- Create table for incomplete business listings if it doesn't exist
CREATE TABLE IF NOT EXISTS incomplete_businesses (
    id SERIAL PRIMARY KEY,
    original_data JSONB NOT NULL,
    missing_fields TEXT NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending_review',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance on incomplete_businesses
CREATE INDEX IF NOT EXISTS idx_incomplete_businesses_status ON incomplete_businesses(status);
CREATE INDEX IF NOT EXISTS idx_incomplete_businesses_created ON incomplete_businesses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_incomplete_businesses_name ON incomplete_businesses(business_name);

-- Add trigger to update updated_at timestamp for incomplete_businesses
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_incomplete_businesses_updated_at') THEN
        CREATE TRIGGER update_incomplete_businesses_updated_at
            BEFORE UPDATE ON incomplete_businesses
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END
$$;
