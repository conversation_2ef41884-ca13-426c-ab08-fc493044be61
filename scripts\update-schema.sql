-- Add new columns to businesses table for Google Places data
ALTER TABLE businesses 
ADD COLUMN IF NOT EXISTS google_maps_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS category_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS country_code VARCHAR(2) DEFAULT 'US';

-- Create index for better search performance
CREATE INDEX IF NOT EXISTS idx_businesses_category ON businesses(category_name);
CREATE INDEX IF NOT EXISTS idx_businesses_country ON businesses(country_code);

-- Update the import_logs table to track more details
ALTER TABLE import_logs 
ADD COLUMN IF NOT EXISTS records_total INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS file_format VARCHAR(50) DEFAULT 'json';
